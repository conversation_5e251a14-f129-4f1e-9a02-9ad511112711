#!/usr/bin/env python3
"""
局域网打印服务启动脚本
"""

import os
import sys
import argparse
from app import create_app

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='局域网打印服务')
    parser.add_argument('--port', '-p', type=int, help='服务端口号')
    parser.add_argument('--host', type=str, help='服务主机地址')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    args = parser.parse_args()

    print("=" * 50)
    print("局域网打印服务")
    print("=" * 50)

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 创建应用
    app = create_app()

    # 获取配置（命令行参数优先）
    host = args.host or app.config.get('HOST', '0.0.0.0')
    port = args.port or app.config.get('PORT', 8080)
    debug = args.debug or app.config.get('DEBUG', False)
    
    print(f"服务器配置:")
    print(f"  主机: {host}")
    print(f"  端口: {port}")
    print(f"  调试模式: {debug}")
    print(f"  访问地址: http://{host}:{port}")
    
    if host == '0.0.0.0':
        import socket
        try:
            # 获取本机IP地址
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            print(f"  局域网访问: http://{local_ip}:{port}")
        except:
            pass
    
    print("\n支持的文件格式:")
    print("  - PDF文档")
    print("  - Word文档 (.docx)")
    print("  - Excel表格 (.xlsx)")
    print("  - 图片文件 (.jpg, .png)")
    print("  - 文本文件 (.txt)")
    
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 启动服务器
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n\n服务已停止")
    except Exception as e:
        print(f"\n启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()

import os
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
from werkzeug.utils import secure_filename
from PyPDF2 import PdfReader
from docx import Document
from openpyxl import load_workbook
from PIL import Image
import uuid
from datetime import datetime


class FileProcessor:
    """文件处理服务"""
    
    def __init__(self, upload_folder, allowed_extensions):
        self.upload_folder = upload_folder
        self.allowed_extensions = allowed_extensions
        
    def allowed_file(self, filename):
        """检查文件扩展名是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def get_file_type(self, file_path):
        """获取文件MIME类型"""
        if HAS_MAGIC:
            try:
                mime = magic.Magic(mime=True)
                return mime.from_file(file_path)
            except:
                pass

        # 如果magic库不可用或失败，根据扩展名判断
        ext = os.path.splitext(file_path)[1].lower()
        mime_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.txt': 'text/plain',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png'
        }
        return mime_types.get(ext, 'application/octet-stream')
    
    def save_uploaded_file(self, file):
        """保存上传的文件"""
        if not file or not file.filename:
            raise ValueError("没有选择文件")
        
        if not self.allowed_file(file.filename):
            raise ValueError(f"不支持的文件类型。支持的格式: {', '.join(self.allowed_extensions)}")
        
        # 生成唯一文件名
        original_filename = secure_filename(file.filename)
        file_ext = os.path.splitext(original_filename)[1]
        unique_filename = f"{uuid.uuid4().hex}{file_ext}"
        
        # 保存文件
        file_path = os.path.join(self.upload_folder, unique_filename)
        file.save(file_path)
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_type = self.get_file_type(file_path)
        
        return {
            'filename': unique_filename,
            'original_filename': original_filename,
            'file_path': file_path,
            'file_size': file_size,
            'file_type': file_type
        }
    
    def get_document_info(self, file_path, file_type):
        """获取文档信息（页数等）"""
        try:
            if 'pdf' in file_type.lower():
                return self._get_pdf_info(file_path)
            elif 'word' in file_type.lower() or file_path.endswith('.docx'):
                return self._get_docx_info(file_path)
            elif 'excel' in file_type.lower() or file_path.endswith('.xlsx'):
                return self._get_xlsx_info(file_path)
            elif 'image' in file_type.lower():
                return self._get_image_info(file_path)
            else:
                return {'pages': 1, 'info': 'Unknown document type'}
        except Exception as e:
            return {'pages': 1, 'info': f'Error reading document: {str(e)}'}
    
    def _get_pdf_info(self, file_path):
        """获取PDF文档信息"""
        try:
            with open(file_path, 'rb') as file:
                reader = PdfReader(file)
                pages = len(reader.pages)
                
                # 获取文档元数据
                metadata = reader.metadata
                info = {
                    'title': metadata.get('/Title', '') if metadata else '',
                    'author': metadata.get('/Author', '') if metadata else '',
                    'creator': metadata.get('/Creator', '') if metadata else ''
                }
                
                return {
                    'pages': pages,
                    'info': info
                }
        except Exception as e:
            return {'pages': 1, 'info': f'Error reading PDF: {str(e)}'}
    
    def _get_docx_info(self, file_path):
        """获取Word文档信息"""
        try:
            doc = Document(file_path)
            
            # 估算页数（基于段落数和内容长度）
            paragraphs = len(doc.paragraphs)
            total_chars = sum(len(p.text) for p in doc.paragraphs)
            estimated_pages = max(1, (total_chars // 2000) + 1)  # 假设每页约2000字符
            
            return {
                'pages': estimated_pages,
                'info': {
                    'paragraphs': paragraphs,
                    'characters': total_chars
                }
            }
        except Exception as e:
            return {'pages': 1, 'info': f'Error reading DOCX: {str(e)}'}
    
    def _get_xlsx_info(self, file_path):
        """获取Excel文档信息"""
        try:
            workbook = load_workbook(file_path, read_only=True)
            sheets = len(workbook.worksheets)
            
            # 计算总行数
            total_rows = 0
            for sheet in workbook.worksheets:
                total_rows += sheet.max_row
            
            return {
                'pages': sheets,  # Excel以工作表为页
                'info': {
                    'sheets': sheets,
                    'total_rows': total_rows
                }
            }
        except Exception as e:
            return {'pages': 1, 'info': f'Error reading XLSX: {str(e)}'}
    
    def _get_image_info(self, file_path):
        """获取图片信息"""
        try:
            with Image.open(file_path) as img:
                width, height = img.size
                format_name = img.format
                
                return {
                    'pages': 1,
                    'info': {
                        'width': width,
                        'height': height,
                        'format': format_name
                    }
                }
        except Exception as e:
            return {'pages': 1, 'info': f'Error reading image: {str(e)}'}
    
    def delete_file(self, file_path):
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
        except Exception as e:
            print(f"Error deleting file {file_path}: {str(e)}")
        return False

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; cursor: pointer; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; max-height: 200px; overflow-y: auto; font-family: monospace; white-space: pre-wrap; font-size: 12px; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 10px 0; cursor: pointer; }
        .upload-area:hover { border-color: #007bff; background: #f0f8ff; }
        .status { padding: 5px 10px; border-radius: 3px; margin: 5px 0; display: inline-block; }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.warn { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🔍 文件上传功能诊断</h1>
    
    <div class="test-section">
        <h3>1. 基础环境检测</h3>
        <div id="envTest">
            <button class="btn" onclick="testEnvironment()">检测环境</button>
            <div id="envResults"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>2. DOM元素检测</h3>
        <div id="domTest">
            <button class="btn" onclick="testDOMElements()">检测DOM元素</button>
            <div id="domResults"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>3. 事件监听器测试</h3>
        <div id="eventTest">
            <div class="upload-area" id="testUploadArea">
                <p>测试上传区域 - 点击这里</p>
                <input type="file" id="testFileInput" style="display: none;">
                <button type="button" class="btn" id="testSelectBtn">测试选择文件</button>
            </div>
            <button class="btn" onclick="setupTestEvents()">设置测试事件</button>
            <div id="eventResults"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>4. 文件上传API测试</h3>
        <div id="apiTest">
            <input type="file" id="apiTestFile" accept=".txt,.pdf,.jpg,.png">
            <button class="btn" onclick="testFileUpload()">测试上传API</button>
            <div id="apiResults"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>5. 实时日志</h3>
        <div id="log" class="log">等待测试...\n</div>
        <button class="btn" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function createStatus(text, type) {
            return `<span class="status ${type}">${text}</span>`;
        }

        function testEnvironment() {
            log('开始环境检测...');
            const results = document.getElementById('envResults');
            let html = '<h4>环境检测结果:</h4>';
            
            // 检测浏览器支持
            const hasFileAPI = !!(window.File && window.FileReader && window.FileList && window.Blob);
            html += `<p>File API支持: ${hasFileAPI ? createStatus('✓ 支持', 'pass') : createStatus('✗ 不支持', 'fail')}</p>`;
            
            const hasFormData = !!window.FormData;
            html += `<p>FormData支持: ${hasFormData ? createStatus('✓ 支持', 'pass') : createStatus('✗ 不支持', 'fail')}</p>`;
            
            const hasFetch = !!window.fetch;
            html += `<p>Fetch API支持: ${hasFetch ? createStatus('✓ 支持', 'pass') : createStatus('✗ 不支持', 'fail')}</p>`;
            
            const hasConsole = !!window.console;
            html += `<p>Console支持: ${hasConsole ? createStatus('✓ 支持', 'pass') : createStatus('✗ 不支持', 'fail')}</p>`;
            
            results.innerHTML = html;
            log('环境检测完成');
        }

        function testDOMElements() {
            log('开始DOM元素检测...');
            const results = document.getElementById('domResults');
            let html = '<h4>DOM元素检测结果:</h4>';
            
            // 检测主页面元素（通过fetch获取）
            fetch('/')
                .then(response => response.text())
                .then(htmlContent => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(htmlContent, 'text/html');
                    
                    const uploadArea = doc.getElementById('uploadArea');
                    const fileInput = doc.getElementById('fileInput');
                    const selectFileBtn = doc.getElementById('selectFileBtn');
                    
                    html += `<p>uploadArea元素: ${uploadArea ? createStatus('✓ 找到', 'pass') : createStatus('✗ 未找到', 'fail')}</p>`;
                    html += `<p>fileInput元素: ${fileInput ? createStatus('✓ 找到', 'pass') : createStatus('✗ 未找到', 'fail')}</p>`;
                    html += `<p>selectFileBtn元素: ${selectFileBtn ? createStatus('✓ 找到', 'pass') : createStatus('✗ 未找到', 'fail')}</p>`;
                    
                    if (uploadArea) {
                        html += `<p>uploadArea类名: <code>${uploadArea.className}</code></p>`;
                    }
                    if (fileInput) {
                        html += `<p>fileInput类型: <code>${fileInput.type}</code></p>`;
                        html += `<p>fileInput accept: <code>${fileInput.accept}</code></p>`;
                    }
                    
                    results.innerHTML = html;
                    log('DOM元素检测完成');
                })
                .catch(error => {
                    html += `<p>检测失败: ${createStatus(error.message, 'fail')}</p>`;
                    results.innerHTML = html;
                    log(`DOM检测失败: ${error.message}`);
                });
        }

        function setupTestEvents() {
            log('设置测试事件监听器...');
            const testUploadArea = document.getElementById('testUploadArea');
            const testFileInput = document.getElementById('testFileInput');
            const testSelectBtn = document.getElementById('testSelectBtn');
            const results = document.getElementById('eventResults');
            
            let html = '<h4>事件测试结果:</h4>';
            
            // 清除之前的事件监听器
            const newUploadArea = testUploadArea.cloneNode(true);
            testUploadArea.parentNode.replaceChild(newUploadArea, testUploadArea);
            
            const newFileInput = newUploadArea.querySelector('#testFileInput');
            const newSelectBtn = newUploadArea.querySelector('#testSelectBtn');
            
            // 设置事件监听器
            newSelectBtn.addEventListener('click', function(e) {
                log('测试按钮被点击');
                e.preventDefault();
                e.stopPropagation();
                newFileInput.click();
                html += `<p>按钮点击事件: ${createStatus('✓ 正常', 'pass')}</p>`;
                results.innerHTML = html;
            });
            
            newUploadArea.addEventListener('click', function(e) {
                if (e.target.tagName !== 'BUTTON') {
                    log('测试上传区域被点击');
                    newFileInput.click();
                    html += `<p>区域点击事件: ${createStatus('✓ 正常', 'pass')}</p>`;
                    results.innerHTML = html;
                }
            });
            
            newFileInput.addEventListener('change', function(e) {
                log(`文件选择事件触发，文件数量: ${e.target.files.length}`);
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    log(`选择的文件: ${file.name} (${file.size} bytes)`);
                    html += `<p>文件选择事件: ${createStatus('✓ 正常', 'pass')}</p>`;
                    html += `<p>选择的文件: <code>${file.name}</code></p>`;
                    results.innerHTML = html;
                }
            });
            
            html += `<p>事件监听器设置: ${createStatus('✓ 完成', 'pass')}</p>`;
            results.innerHTML = html;
            log('测试事件监听器设置完成');
        }

        function testFileUpload() {
            log('开始API上传测试...');
            const fileInput = document.getElementById('apiTestFile');
            const results = document.getElementById('apiResults');
            
            if (!fileInput.files || fileInput.files.length === 0) {
                results.innerHTML = '<p>' + createStatus('请先选择一个文件', 'warn') + '</p>';
                log('未选择文件');
                return;
            }
            
            const file = fileInput.files[0];
            log(`开始上传文件: ${file.name}`);
            
            const formData = new FormData();
            formData.append('file', file);
            
            let html = '<h4>API测试结果:</h4>';
            html += `<p>上传文件: <code>${file.name}</code> (${(file.size/1024/1024).toFixed(2)} MB)</p>`;
            results.innerHTML = html;
            
            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                log(`服务器响应: ${response.status} ${response.statusText}`);
                html += `<p>HTTP状态: ${response.ok ? createStatus(`${response.status} OK`, 'pass') : createStatus(`${response.status} Error`, 'fail')}</p>`;
                results.innerHTML = html;
                return response.json();
            })
            .then(data => {
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                html += `<p>上传结果: ${data.success ? createStatus('✓ 成功', 'pass') : createStatus('✗ 失败', 'fail')}</p>`;
                if (data.error) {
                    html += `<p>错误信息: <code>${data.error}</code></p>`;
                }
                if (data.file_info) {
                    html += `<p>文件信息: <code>${JSON.stringify(data.file_info, null, 2)}</code></p>`;
                }
                results.innerHTML = html;
                log('API测试完成');
            })
            .catch(error => {
                log(`API测试失败: ${error.message}`);
                html += `<p>请求失败: ${createStatus(error.message, 'fail')}</p>`;
                results.innerHTML = html;
            });
        }

        // 页面加载时自动运行基础检测
        document.addEventListener('DOMContentLoaded', function() {
            log('诊断页面加载完成');
            testEnvironment();
            setTimeout(testDOMElements, 500);
        });
    </script>
</body>
</html>

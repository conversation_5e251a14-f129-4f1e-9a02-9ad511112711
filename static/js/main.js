// 全局变量
let currentFile = null;
let currentPreview = null;
let currentZoom = 1;
let printers = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, starting initialization...');
    // 延迟初始化，确保所有资源加载完成
    setTimeout(initializeApp, 100);
});

// 备用初始化（如果DOMContentLoaded已经触发）
if (document.readyState === 'loading') {
    // 文档仍在加载中
} else {
    // 文档已经加载完成
    setTimeout(initializeApp, 100);
}

// 初始化应用
function initializeApp() {
    console.log('Initializing app...');
    setupFileUpload();
    setupPrintConfig();
    loadPrinters();
    loadRecentJobs();

    // 定期刷新任务状态
    setInterval(loadRecentJobs, 10000); // 每10秒刷新一次
    console.log('App initialization complete');
}

// 设置文件上传
function setupFileUpload() {
    console.log('Setting up file upload...');

    // 使用更强健的元素查找方法
    let uploadArea = document.getElementById('uploadArea');
    let fileInput = document.getElementById('fileInput');
    let selectFileBtn = document.getElementById('selectFileBtn');

    // 如果元素未找到，尝试等待并重试
    if (!uploadArea || !fileInput) {
        console.warn('Upload elements not found, retrying in 500ms...');
        setTimeout(setupFileUpload, 500);
        return;
    }

    console.log('Elements found:', { uploadArea, fileInput, selectFileBtn });

    // 选择文件按钮点击事件
    if (selectFileBtn) {
        console.log('Setting up select file button event');
        selectFileBtn.addEventListener('click', function(e) {
            console.log('Select file button clicked');
            e.preventDefault();
            e.stopPropagation();
            fileInput.click();
        });
    } else {
        console.warn('Select file button not found');
    }

    // 点击上传区域（避免点击按钮时重复触发）
    uploadArea.addEventListener('click', function(e) {
        console.log('Upload area clicked', e.target);
        // 如果点击的是按钮或按钮内的元素，不要重复触发
        if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
            console.log('Button clicked, not triggering file input');
            return;
        }
        console.log('Triggering file input from upload area');
        e.preventDefault();
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function(e) {
        console.log('File input change event', e.target.files);
        if (e.target.files && e.target.files.length > 0) {
            const file = e.target.files[0];
            console.log('File selected:', file.name, file.size, file.type);
            if (validateFile(file)) {
                console.log('File validation passed, starting upload');
                uploadFile(file);
            } else {
                console.log('File validation failed');
            }
        } else {
            console.log('No files selected');
        }
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        // 只有当鼠标真正离开uploadArea时才移除样式
        if (!uploadArea.contains(e.relatedTarget)) {
            uploadArea.classList.remove('dragover');
        }
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files && files.length > 0) {
            const file = files[0];
            if (validateFile(file)) {
                uploadFile(file);
            }
        }
    });

    // 防止页面默认的拖拽行为
    document.addEventListener('dragover', function(e) {
        e.preventDefault();
    });

    document.addEventListener('drop', function(e) {
        e.preventDefault();
    });
}

// 验证文件
function validateFile(file) {
    // 检查文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
        showToast('文件太大，请选择小于50MB的文件', 'error');
        return false;
    }

    // 检查文件类型
    const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/plain',
        'image/jpeg',
        'image/jpg',
        'image/png'
    ];

    const allowedExtensions = ['.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt', '.jpg', '.jpeg', '.png'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
        showToast('不支持的文件格式。支持的格式: PDF, Word, Excel, 图片, 文本', 'error');
        return false;
    }

    return true;
}

// 设置打印配置
function setupPrintConfig() {
    // 页面范围选择
    document.querySelectorAll('input[name="pageRange"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const customPageRange = document.getElementById('customPageRange');
            if (this.value === 'custom') {
                customPageRange.classList.remove('d-none');
            } else {
                customPageRange.classList.add('d-none');
            }
        });
    });
}

// 上传文件
function uploadFile(file) {
    // 防止重复上传
    if (window.uploadInProgress) {
        showToast('文件正在上传中，请稍候...', 'warning');
        return;
    }

    console.log('开始上传文件:', file.name, '大小:', formatFileSize(file.size));

    // 重置之前的状态
    resetUploadState();

    const formData = new FormData();
    formData.append('file', file);

    // 设置上传状态
    window.uploadInProgress = true;

    // 显示上传进度
    showUploadProgress();

    // 创建AbortController用于取消请求
    const controller = new AbortController();
    window.uploadController = controller;

    fetch('/api/upload', {
        method: 'POST',
        body: formData,
        signal: controller.signal
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('上传响应:', data);

        if (data.success) {
            currentFile = data.file_info;
            currentPreview = data.preview;

            showFileInfo(data.file_info, data.document_info);
            showPreview(data.preview);
            enablePrintConfig();

            showToast('文件上传成功！', 'success');
        } else {
            throw new Error(data.error || '服务器返回错误');
        }
    })
    .catch(error => {
        console.error('Upload error:', error);

        if (error.name === 'AbortError') {
            showToast('上传已取消', 'info');
        } else if (error.message.includes('HTTP 413')) {
            showToast('文件太大，请选择小于50MB的文件', 'error');
        } else if (error.message.includes('HTTP 400')) {
            showToast('文件格式不支持或文件损坏', 'error');
        } else {
            showToast(error.message || '上传失败，请重试', 'error');
        }
    })
    .finally(() => {
        hideUploadProgress();
        window.uploadInProgress = false;
        window.uploadController = null;
    });
}

// 重置上传状态
function resetUploadState() {
    // 隐藏之前的文件信息和预览
    const fileInfoCard = document.getElementById('fileInfoCard');
    const previewCard = document.getElementById('previewCard');
    const printConfigCard = document.getElementById('printConfigCard');

    if (fileInfoCard) fileInfoCard.classList.add('d-none');
    if (previewCard) previewCard.classList.add('d-none');
    if (printConfigCard) printConfigCard.classList.add('d-none');

    // 重置全局变量
    currentFile = null;
    currentPreview = null;
    currentZoom = 1;

    // 更新打印按钮状态
    updatePrintButton();
}

// 显示上传进度
function showUploadProgress() {
    const uploadProgress = document.getElementById('uploadProgress');
    if (!uploadProgress) return;

    uploadProgress.classList.remove('d-none');

    // 添加取消按钮
    const progressText = uploadProgress.querySelector('small');
    if (progressText && !progressText.querySelector('.cancel-btn')) {
        progressText.innerHTML = `
            正在上传文件...
            <button type="button" class="btn btn-sm btn-outline-danger ms-2 cancel-btn" onclick="cancelUpload()">
                取消
            </button>
        `;
    }

    // 模拟进度条动画
    const progressBar = uploadProgress.querySelector('.progress-bar');
    if (progressBar) {
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 20 + 5; // 更平滑的进度
            if (progress > 85) progress = 85; // 不要到100%，等真正完成再设置
            progressBar.style.width = progress + '%';

            // 如果上传已完成，清除interval
            if (!window.uploadInProgress) {
                clearInterval(interval);
            }
        }, 300);

        // 保存interval ID以便后续清除
        uploadProgress.dataset.interval = interval;
    }
}

// 取消上传
function cancelUpload() {
    if (window.uploadController) {
        window.uploadController.abort();
        showToast('上传已取消', 'info');
    }
}

// 隐藏上传进度
function hideUploadProgress() {
    const uploadProgress = document.getElementById('uploadProgress');
    if (!uploadProgress) return;

    const interval = uploadProgress.dataset.interval;
    if (interval) {
        clearInterval(interval);
        uploadProgress.removeAttribute('data-interval');
    }

    // 完成进度条
    const progressBar = uploadProgress.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.style.width = '100%';
    }

    // 重置进度文本
    const progressText = uploadProgress.querySelector('small');
    if (progressText) {
        progressText.innerHTML = '正在上传文件...';
    }

    setTimeout(() => {
        uploadProgress.classList.add('d-none');
        if (progressBar) {
            progressBar.style.width = '0%';
        }
    }, 800);
}

// 显示文件信息
function showFileInfo(fileInfo, docInfo) {
    const fileInfoCard = document.getElementById('fileInfoCard');
    const fileInfoDiv = document.getElementById('fileInfo');

    if (!fileInfoCard || !fileInfoDiv) {
        console.error('File info elements not found');
        return;
    }

    // 安全地获取文档信息
    const pages = docInfo && docInfo.pages ? docInfo.pages : '未知';
    const docInfoText = docInfo && docInfo.info ?
        (typeof docInfo.info === 'object' ? JSON.stringify(docInfo.info) : docInfo.info) :
        '无额外信息';

    const html = `
        <div class="row">
            <div class="col-md-6">
                <strong>文件名:</strong> ${escapeHtml(fileInfo.original_filename)}<br>
                <strong>文件大小:</strong> ${formatFileSize(fileInfo.file_size)}<br>
                <strong>文件类型:</strong> ${escapeHtml(fileInfo.file_type)}
            </div>
            <div class="col-md-6">
                <strong>页数:</strong> ${pages}<br>
                <strong>上传时间:</strong> ${new Date().toLocaleString('zh-CN')}<br>
                <strong>文档信息:</strong> <small class="text-muted">${escapeHtml(docInfoText)}</small>
            </div>
        </div>
    `;

    fileInfoDiv.innerHTML = html;
    fileInfoCard.classList.remove('d-none');

    // 添加动画效果
    fileInfoCard.style.opacity = '0';
    fileInfoCard.style.transform = 'translateY(20px)';
    setTimeout(() => {
        fileInfoCard.style.transition = 'all 0.3s ease';
        fileInfoCard.style.opacity = '1';
        fileInfoCard.style.transform = 'translateY(0)';
    }, 100);
}

// HTML转义函数
function escapeHtml(text) {
    if (typeof text !== 'string') return text;
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示预览
function showPreview(previewData) {
    const previewCard = document.getElementById('previewCard');
    const previewContainer = document.getElementById('previewContainer');
    const previewPagination = document.getElementById('previewPagination');

    if (!previewCard || !previewContainer || !previewPagination) {
        console.error('Preview elements not found');
        return;
    }

    if (!previewData || !previewData.success) {
        const errorMsg = previewData && previewData.error ? previewData.error : '预览生成失败';
        showToast('预览生成失败: ' + errorMsg, 'warning');

        // 显示预览失败的提示
        previewContainer.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>预览生成失败</h5>
                <p class="text-muted">${escapeHtml(errorMsg)}</p>
                <small class="text-muted">文件仍可正常打印</small>
            </div>
        `;
        previewCard.classList.remove('d-none');
        return;
    }

    // 检查是否有预览文件
    if (!previewData.files || previewData.files.length === 0) {
        previewContainer.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-file fa-3x text-muted mb-3"></i>
                <h5>无法生成预览</h5>
                <p class="text-muted">此文件类型暂不支持预览</p>
                <small class="text-muted">文件仍可正常打印</small>
            </div>
        `;
        previewCard.classList.remove('d-none');
        return;
    }

    // 显示预览图片
    let html = '';
    previewData.files.forEach((file, index) => {
        html += `
            <div class="preview-page" data-page="${file.page}">
                <img src="/api/preview/${escapeHtml(file.filename)}"
                     class="preview-image"
                     alt="第${file.page}页预览"
                     style="transform: scale(${currentZoom})"
                     onerror="this.parentElement.innerHTML='<div class=\\'text-center p-3\\'><i class=\\'fas fa-image fa-2x text-muted\\'></i><br><small>图片加载失败</small></div>'">
                <div class="preview-page-number">第 ${file.page} 页</div>
            </div>
        `;
    });

    previewContainer.innerHTML = html;

    // 显示分页信息
    const totalPages = previewData.total_pages || previewData.files.length;
    const previewPages = previewData.preview_pages || previewData.files.length;

    if (totalPages > 1) {
        let paginationHtml = `
            <small class="text-muted">
                显示 ${previewPages} / ${totalPages} 页
            `;

        if (previewData.note) {
            paginationHtml += `<br><em>${escapeHtml(previewData.note)}</em>`;
        }

        paginationHtml += '</small>';
        previewPagination.innerHTML = paginationHtml;
    } else {
        previewPagination.innerHTML = previewData.note ?
            `<small class="text-muted"><em>${escapeHtml(previewData.note)}</em></small>` : '';
    }

    previewCard.classList.remove('d-none');

    // 添加动画效果
    previewCard.style.opacity = '0';
    previewCard.style.transform = 'translateY(20px)';
    setTimeout(() => {
        previewCard.style.transition = 'all 0.3s ease';
        previewCard.style.opacity = '1';
        previewCard.style.transform = 'translateY(0)';
    }, 200);
}

// 缩放控制
function zoomIn() {
    currentZoom = Math.min(currentZoom * 1.2, 3);
    updatePreviewZoom();
}

function zoomOut() {
    currentZoom = Math.max(currentZoom / 1.2, 0.3);
    updatePreviewZoom();
}

function resetZoom() {
    currentZoom = 1;
    updatePreviewZoom();
}

function updatePreviewZoom() {
    document.querySelectorAll('.preview-image').forEach(img => {
        img.style.transform = `scale(${currentZoom})`;
    });
}

// 启用打印配置
function enablePrintConfig() {
    document.getElementById('printConfigCard').classList.remove('d-none');
    updatePrintButton();
}

// 加载打印机列表
function loadPrinters() {
    fetch('/api/printers')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            printers = data.printers;
            updatePrinterSelect();
        } else {
            showToast('获取打印机列表失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Load printers error:', error);
        showToast('获取打印机列表失败', 'error');
    });
}

// 更新打印机选择框
function updatePrinterSelect() {
    const printerSelect = document.getElementById('printerSelect');
    
    if (printers.length === 0) {
        printerSelect.innerHTML = '<option value="">未找到可用打印机</option>';
        return;
    }
    
    let html = '<option value="">请选择打印机</option>';
    printers.forEach(printer => {
        html += `<option value="${printer.name}">${printer.display_name}</option>`;
    });
    
    printerSelect.innerHTML = html;
    printerSelect.addEventListener('change', updatePrintButton);
}

// 刷新打印机列表
function refreshPrinters() {
    const refreshBtn = document.querySelector('button[onclick="refreshPrinters()"] i');
    refreshBtn.classList.add('fa-spin');
    
    loadPrinters();
    
    setTimeout(() => {
        refreshBtn.classList.remove('fa-spin');
    }, 1000);
}

// 更新打印按钮状态
function updatePrintButton() {
    const printButton = document.getElementById('printButton');
    const printerSelect = document.getElementById('printerSelect');
    
    const hasFile = currentFile !== null;
    const hasPrinter = printerSelect.value !== '';
    
    printButton.disabled = !(hasFile && hasPrinter);
}

// 提交打印任务
function submitPrintJob() {
    if (!currentFile) {
        showToast('请先上传文件', 'warning');
        return;
    }
    
    const printerSelect = document.getElementById('printerSelect');
    if (!printerSelect.value) {
        showToast('请选择打印机', 'warning');
        return;
    }
    
    // 收集打印配置
    const printConfig = {
        filename: currentFile.filename,
        original_filename: currentFile.original_filename,
        printer_name: printerSelect.value,
        copies: parseInt(document.getElementById('copies').value),
        color_mode: document.querySelector('input[name="colorMode"]:checked').value,
        duplex: document.querySelector('input[name="duplex"]:checked').value,
        paper_size: document.getElementById('paperSize').value,
        orientation: document.querySelector('input[name="orientation"]:checked').value
    };
    
    // 处理页面范围
    const pageRangeType = document.querySelector('input[name="pageRange"]:checked').value;
    if (pageRangeType === 'custom') {
        const customRange = document.getElementById('customPageRange').value.trim();
        if (customRange) {
            printConfig.page_range = customRange;
        }
    }
    
    // 提交打印任务
    const printButton = document.getElementById('printButton');
    const originalText = printButton.innerHTML;
    printButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
    printButton.disabled = true;
    
    fetch('/api/print', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(printConfig)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('打印任务已提交！', 'success');
            loadRecentJobs(); // 刷新任务列表
            
            // 重置表单
            resetForm();
        } else {
            showToast(data.error || '提交打印任务失败', 'error');
        }
    })
    .catch(error => {
        console.error('Submit print job error:', error);
        showToast('提交打印任务失败', 'error');
    })
    .finally(() => {
        printButton.innerHTML = originalText;
        printButton.disabled = false;
    });
}

// 重置表单
function resetForm() {
    currentFile = null;
    currentPreview = null;
    currentZoom = 1;
    
    // 隐藏卡片
    document.getElementById('fileInfoCard').classList.add('d-none');
    document.getElementById('previewCard').classList.add('d-none');
    document.getElementById('printConfigCard').classList.add('d-none');
    
    // 重置文件输入
    document.getElementById('fileInput').value = '';
    
    // 重置打印配置
    document.getElementById('copies').value = 1;
    document.querySelector('input[name="pageRange"][value="all"]').checked = true;
    document.getElementById('customPageRange').classList.add('d-none');
    document.getElementById('customPageRange').value = '';
    document.querySelector('input[name="colorMode"][value="color"]').checked = true;
    document.querySelector('input[name="duplex"][value="none"]').checked = true;
    document.getElementById('paperSize').value = 'A4';
    document.querySelector('input[name="orientation"][value="portrait"]').checked = true;
    
    updatePrintButton();
}

// 加载最近的打印任务
function loadRecentJobs() {
    fetch('/api/jobs')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateRecentJobs(data.jobs.slice(0, 5)); // 只显示最近5个任务
        }
    })
    .catch(error => {
        console.error('Load recent jobs error:', error);
    });
}

// 更新最近任务显示
function updateRecentJobs(jobs) {
    const recentJobsDiv = document.getElementById('recentJobs');
    
    if (jobs.length === 0) {
        recentJobsDiv.innerHTML = '<p class="text-muted text-center">暂无打印任务</p>';
        return;
    }
    
    let html = '';
    jobs.forEach(job => {
        html += `
            <div class="border-bottom pb-2 mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <small class="fw-bold">${job.original_filename}</small><br>
                        <small class="text-muted">${job.printer_name}</small><br>
                        <small class="job-status ${job.status}">${getStatusText(job.status)}</small>
                    </div>
                    <small class="text-muted">${formatDateTime(job.created_at)}</small>
                </div>
            </div>
        `;
    });
    
    recentJobsDiv.innerHTML = html;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'printing': '打印中',
        'completed': '已完成',
        'failed': '失败',
        'cancelled': '已取消'
    };
    return statusMap[status] || status;
}

// 显示打印任务列表
function showPrintJobs() {
    const modal = new bootstrap.Modal(document.getElementById('jobsModal'));
    modal.show();
    
    // 加载完整任务列表
    loadAllJobs();
}

// 加载所有打印任务
function loadAllJobs() {
    const jobsList = document.getElementById('jobsList');
    jobsList.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    
    fetch('/api/jobs')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAllJobs(data.jobs);
        } else {
            jobsList.innerHTML = '<p class="text-center text-muted">加载失败</p>';
        }
    })
    .catch(error => {
        console.error('Load all jobs error:', error);
        jobsList.innerHTML = '<p class="text-center text-muted">加载失败</p>';
    });
}

// 显示所有任务
function displayAllJobs(jobs) {
    const jobsList = document.getElementById('jobsList');
    
    if (jobs.length === 0) {
        jobsList.innerHTML = '<p class="text-center text-muted">暂无打印任务</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += `
        <thead>
            <tr>
                <th>文件名</th>
                <th>打印机</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
    `;
    
    jobs.forEach(job => {
        html += `
            <tr>
                <td>
                    <div class="fw-bold">${job.original_filename}</div>
                    <small class="text-muted">${formatFileSize(job.file_size)}</small>
                </td>
                <td>${job.printer_name}</td>
                <td><span class="job-status ${job.status}">${getStatusText(job.status)}</span></td>
                <td>${formatDateTime(job.created_at)}</td>
                <td>
                    ${job.status === 'pending' || job.status === 'printing' ? 
                        `<button class="btn btn-sm btn-outline-danger" onclick="cancelJob(${job.id})">
                            <i class="fas fa-times"></i> 取消
                        </button>` : 
                        '-'
                    }
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    jobsList.innerHTML = html;
}

// 取消打印任务
function cancelJob(jobId) {
    if (!confirm('确定要取消这个打印任务吗？')) {
        return;
    }
    
    fetch(`/api/jobs/${jobId}/cancel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('任务已取消', 'success');
            loadAllJobs(); // 刷新任务列表
            loadRecentJobs(); // 刷新最近任务
        } else {
            showToast(data.error || '取消失败', 'error');
        }
    })
    .catch(error => {
        console.error('Cancel job error:', error);
        showToast('取消失败', 'error');
    });
}

// 全局备用函数 - 确保文件上传功能可用
window.triggerFileSelect = function() {
    console.log('Global file select triggered');
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.click();
    } else {
        console.error('File input not found');
    }
};

window.handleFileChange = function(input) {
    console.log('Global file change handler', input.files);
    if (input.files && input.files.length > 0) {
        const file = input.files[0];
        console.log('File selected via global handler:', file.name);
        if (typeof validateFile === 'function' && validateFile(file)) {
            if (typeof uploadFile === 'function') {
                uploadFile(file);
            } else {
                console.error('uploadFile function not available');
            }
        }
    }
};

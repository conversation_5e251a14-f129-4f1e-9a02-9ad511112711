// 全局变量
let currentFile = null;
let currentPreview = null;
let currentZoom = 1;
let printers = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    initializeApp();
});

// 初始化应用
function initializeApp() {
    console.log('Initializing app...');
    setupFileUpload();
    setupPrintConfig();
    loadPrinters();
    loadRecentJobs();

    // 定期刷新任务状态
    setInterval(loadRecentJobs, 10000); // 每10秒刷新一次
    console.log('App initialization complete');
}

// 设置文件上传
function setupFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');

    console.log('Setting up file upload...', { uploadArea, fileInput });

    if (!uploadArea || !fileInput) {
        console.error('Upload elements not found!', { uploadArea, fileInput });
        return;
    }

    // 点击上传区域
    uploadArea.addEventListener('click', function() {
        console.log('Upload area clicked');
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function(e) {
        console.log('File input changed', e.target.files);
        if (e.target.files.length > 0) {
            console.log('Uploading file:', e.target.files[0]);
            uploadFile(e.target.files[0]);
        }
    });
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        if (e.dataTransfer.files.length > 0) {
            uploadFile(e.dataTransfer.files[0]);
        }
    });
}

// 设置打印配置
function setupPrintConfig() {
    // 页面范围选择
    document.querySelectorAll('input[name="pageRange"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const customPageRange = document.getElementById('customPageRange');
            if (this.value === 'custom') {
                customPageRange.classList.remove('d-none');
            } else {
                customPageRange.classList.add('d-none');
            }
        });
    });
}

// 上传文件
function uploadFile(file) {
    console.log('uploadFile called with:', file);

    const formData = new FormData();
    formData.append('file', file);

    console.log('FormData created, starting upload...');

    // 显示上传进度
    showUploadProgress();

    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideUploadProgress();
        
        if (data.success) {
            currentFile = data.file_info;
            currentPreview = data.preview;
            
            showFileInfo(data.file_info, data.document_info);
            showPreview(data.preview);
            enablePrintConfig();
            
            showToast('文件上传成功！', 'success');
        } else {
            showToast(data.error || '上传失败', 'error');
        }
    })
    .catch(error => {
        hideUploadProgress();
        console.error('Upload error:', error);
        showToast('上传失败，请重试', 'error');
    });
}

// 显示上传进度
function showUploadProgress() {
    document.getElementById('uploadProgress').classList.remove('d-none');
    
    // 模拟进度条动画
    const progressBar = document.querySelector('.progress-bar');
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);
    
    // 保存interval ID以便后续清除
    document.getElementById('uploadProgress').dataset.interval = interval;
}

// 隐藏上传进度
function hideUploadProgress() {
    const uploadProgress = document.getElementById('uploadProgress');
    const interval = uploadProgress.dataset.interval;
    
    if (interval) {
        clearInterval(interval);
    }
    
    // 完成进度条
    const progressBar = document.querySelector('.progress-bar');
    progressBar.style.width = '100%';
    
    setTimeout(() => {
        uploadProgress.classList.add('d-none');
        progressBar.style.width = '0%';
    }, 500);
}

// 显示文件信息
function showFileInfo(fileInfo, docInfo) {
    const fileInfoCard = document.getElementById('fileInfoCard');
    const fileInfoDiv = document.getElementById('fileInfo');
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <strong>文件名:</strong> ${fileInfo.original_filename}<br>
                <strong>文件大小:</strong> ${formatFileSize(fileInfo.file_size)}<br>
                <strong>文件类型:</strong> ${fileInfo.file_type}
            </div>
            <div class="col-md-6">
                <strong>页数:</strong> ${docInfo.pages}<br>
                <strong>上传时间:</strong> ${new Date().toLocaleString('zh-CN')}
            </div>
        </div>
    `;
    
    fileInfoDiv.innerHTML = html;
    fileInfoCard.classList.remove('d-none');
}

// 显示预览
function showPreview(previewData) {
    if (!previewData.success) {
        showToast('预览生成失败: ' + previewData.error, 'warning');
        return;
    }
    
    const previewCard = document.getElementById('previewCard');
    const previewContainer = document.getElementById('previewContainer');
    const previewPagination = document.getElementById('previewPagination');
    
    // 显示预览图片
    let html = '';
    previewData.files.forEach((file, index) => {
        html += `
            <div class="preview-page" data-page="${file.page}">
                <img src="/api/preview/${file.filename}" 
                     class="preview-image" 
                     alt="第${file.page}页预览"
                     style="transform: scale(${currentZoom})">
            </div>
        `;
    });
    
    previewContainer.innerHTML = html;
    
    // 显示分页信息
    if (previewData.files.length > 1) {
        previewPagination.innerHTML = `
            <small class="text-muted">
                显示 ${previewData.preview_pages} / ${previewData.total_pages} 页
            </small>
        `;
    } else {
        previewPagination.innerHTML = '';
    }
    
    previewCard.classList.remove('d-none');
}

// 缩放控制
function zoomIn() {
    currentZoom = Math.min(currentZoom * 1.2, 3);
    updatePreviewZoom();
}

function zoomOut() {
    currentZoom = Math.max(currentZoom / 1.2, 0.3);
    updatePreviewZoom();
}

function resetZoom() {
    currentZoom = 1;
    updatePreviewZoom();
}

function updatePreviewZoom() {
    document.querySelectorAll('.preview-image').forEach(img => {
        img.style.transform = `scale(${currentZoom})`;
    });
}

// 启用打印配置
function enablePrintConfig() {
    document.getElementById('printConfigCard').classList.remove('d-none');
    updatePrintButton();
}

// 加载打印机列表
function loadPrinters() {
    fetch('/api/printers')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            printers = data.printers;
            updatePrinterSelect();
        } else {
            showToast('获取打印机列表失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Load printers error:', error);
        showToast('获取打印机列表失败', 'error');
    });
}

// 更新打印机选择框
function updatePrinterSelect() {
    const printerSelect = document.getElementById('printerSelect');
    
    if (printers.length === 0) {
        printerSelect.innerHTML = '<option value="">未找到可用打印机</option>';
        return;
    }
    
    let html = '<option value="">请选择打印机</option>';
    printers.forEach(printer => {
        html += `<option value="${printer.name}">${printer.display_name}</option>`;
    });
    
    printerSelect.innerHTML = html;
    printerSelect.addEventListener('change', updatePrintButton);
}

// 刷新打印机列表
function refreshPrinters() {
    const refreshBtn = document.querySelector('button[onclick="refreshPrinters()"] i');
    refreshBtn.classList.add('fa-spin');
    
    loadPrinters();
    
    setTimeout(() => {
        refreshBtn.classList.remove('fa-spin');
    }, 1000);
}

// 更新打印按钮状态
function updatePrintButton() {
    const printButton = document.getElementById('printButton');
    const printerSelect = document.getElementById('printerSelect');
    
    const hasFile = currentFile !== null;
    const hasPrinter = printerSelect.value !== '';
    
    printButton.disabled = !(hasFile && hasPrinter);
}

// 提交打印任务
function submitPrintJob() {
    if (!currentFile) {
        showToast('请先上传文件', 'warning');
        return;
    }
    
    const printerSelect = document.getElementById('printerSelect');
    if (!printerSelect.value) {
        showToast('请选择打印机', 'warning');
        return;
    }
    
    // 收集打印配置
    const printConfig = {
        filename: currentFile.filename,
        original_filename: currentFile.original_filename,
        printer_name: printerSelect.value,
        copies: parseInt(document.getElementById('copies').value),
        color_mode: document.querySelector('input[name="colorMode"]:checked').value,
        duplex: document.querySelector('input[name="duplex"]:checked').value,
        paper_size: document.getElementById('paperSize').value,
        orientation: document.querySelector('input[name="orientation"]:checked').value
    };
    
    // 处理页面范围
    const pageRangeType = document.querySelector('input[name="pageRange"]:checked').value;
    if (pageRangeType === 'custom') {
        const customRange = document.getElementById('customPageRange').value.trim();
        if (customRange) {
            printConfig.page_range = customRange;
        }
    }
    
    // 提交打印任务
    const printButton = document.getElementById('printButton');
    const originalText = printButton.innerHTML;
    printButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
    printButton.disabled = true;
    
    fetch('/api/print', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(printConfig)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('打印任务已提交！', 'success');
            loadRecentJobs(); // 刷新任务列表
            
            // 重置表单
            resetForm();
        } else {
            showToast(data.error || '提交打印任务失败', 'error');
        }
    })
    .catch(error => {
        console.error('Submit print job error:', error);
        showToast('提交打印任务失败', 'error');
    })
    .finally(() => {
        printButton.innerHTML = originalText;
        printButton.disabled = false;
    });
}

// 重置表单
function resetForm() {
    currentFile = null;
    currentPreview = null;
    currentZoom = 1;
    
    // 隐藏卡片
    document.getElementById('fileInfoCard').classList.add('d-none');
    document.getElementById('previewCard').classList.add('d-none');
    document.getElementById('printConfigCard').classList.add('d-none');
    
    // 重置文件输入
    document.getElementById('fileInput').value = '';
    
    // 重置打印配置
    document.getElementById('copies').value = 1;
    document.querySelector('input[name="pageRange"][value="all"]').checked = true;
    document.getElementById('customPageRange').classList.add('d-none');
    document.getElementById('customPageRange').value = '';
    document.querySelector('input[name="colorMode"][value="color"]').checked = true;
    document.querySelector('input[name="duplex"][value="none"]').checked = true;
    document.getElementById('paperSize').value = 'A4';
    document.querySelector('input[name="orientation"][value="portrait"]').checked = true;
    
    updatePrintButton();
}

// 加载最近的打印任务
function loadRecentJobs() {
    fetch('/api/jobs')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateRecentJobs(data.jobs.slice(0, 5)); // 只显示最近5个任务
        }
    })
    .catch(error => {
        console.error('Load recent jobs error:', error);
    });
}

// 更新最近任务显示
function updateRecentJobs(jobs) {
    const recentJobsDiv = document.getElementById('recentJobs');
    
    if (jobs.length === 0) {
        recentJobsDiv.innerHTML = '<p class="text-muted text-center">暂无打印任务</p>';
        return;
    }
    
    let html = '';
    jobs.forEach(job => {
        html += `
            <div class="border-bottom pb-2 mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <small class="fw-bold">${job.original_filename}</small><br>
                        <small class="text-muted">${job.printer_name}</small><br>
                        <small class="job-status ${job.status}">${getStatusText(job.status)}</small>
                    </div>
                    <small class="text-muted">${formatDateTime(job.created_at)}</small>
                </div>
            </div>
        `;
    });
    
    recentJobsDiv.innerHTML = html;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'printing': '打印中',
        'completed': '已完成',
        'failed': '失败',
        'cancelled': '已取消'
    };
    return statusMap[status] || status;
}

// 显示打印任务列表
function showPrintJobs() {
    const modal = new bootstrap.Modal(document.getElementById('jobsModal'));
    modal.show();
    
    // 加载完整任务列表
    loadAllJobs();
}

// 加载所有打印任务
function loadAllJobs() {
    const jobsList = document.getElementById('jobsList');
    jobsList.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    
    fetch('/api/jobs')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAllJobs(data.jobs);
        } else {
            jobsList.innerHTML = '<p class="text-center text-muted">加载失败</p>';
        }
    })
    .catch(error => {
        console.error('Load all jobs error:', error);
        jobsList.innerHTML = '<p class="text-center text-muted">加载失败</p>';
    });
}

// 显示所有任务
function displayAllJobs(jobs) {
    const jobsList = document.getElementById('jobsList');
    
    if (jobs.length === 0) {
        jobsList.innerHTML = '<p class="text-center text-muted">暂无打印任务</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += `
        <thead>
            <tr>
                <th>文件名</th>
                <th>打印机</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
    `;
    
    jobs.forEach(job => {
        html += `
            <tr>
                <td>
                    <div class="fw-bold">${job.original_filename}</div>
                    <small class="text-muted">${formatFileSize(job.file_size)}</small>
                </td>
                <td>${job.printer_name}</td>
                <td><span class="job-status ${job.status}">${getStatusText(job.status)}</span></td>
                <td>${formatDateTime(job.created_at)}</td>
                <td>
                    ${job.status === 'pending' || job.status === 'printing' ? 
                        `<button class="btn btn-sm btn-outline-danger" onclick="cancelJob(${job.id})">
                            <i class="fas fa-times"></i> 取消
                        </button>` : 
                        '-'
                    }
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    jobsList.innerHTML = html;
}

// 取消打印任务
function cancelJob(jobId) {
    if (!confirm('确定要取消这个打印任务吗？')) {
        return;
    }
    
    fetch(`/api/jobs/${jobId}/cancel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('任务已取消', 'success');
            loadAllJobs(); // 刷新任务列表
            loadRecentJobs(); // 刷新最近任务
        } else {
            showToast(data.error || '取消失败', 'error');
        }
    })
    .catch(error => {
        console.error('Cancel job error:', error);
        showToast('取消失败', 'error');
    });
}

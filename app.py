import os
import json
import threading
import time
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, render_template, send_from_directory, url_for
from werkzeug.exceptions import RequestEntityTooLarge

from config import config
from models.database import db, PrintJob, PrinterConfig
from services.file_processor import FileProcessor
from services.printer_service import PrinterService
from services.preview_service import PreviewService
from utils.file_cleaner import init_file_cleaner


def create_app(config_name=None):
    """创建Flask应用"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化配置
    config[config_name].init_app(app)
    
    # 初始化数据库
    db.init_app(app)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    # 初始化服务
    file_processor = FileProcessor(
        app.config['UPLOAD_FOLDER'],
        app.config['ALLOWED_EXTENSIONS']
    )

    printer_service = PrinterService()

    preview_service = PreviewService(
        app.config['PREVIEW_FOLDER'],
        app.config['PREVIEW_DPI'],
        app.config['PREVIEW_MAX_PAGES']
    )

    # 初始化文件清理器
    init_file_cleaner(app)
    
    # 路由定义
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')

    @app.route('/debug')
    def debug():
        """调试页面"""
        return send_from_directory('.', 'debug_upload.html')

    @app.route('/test')
    def test():
        """简单测试页面"""
        return send_from_directory('.', 'test_simple_upload.html')

    @app.route('/diagnose')
    def diagnose():
        """诊断页面"""
        return send_from_directory('.', 'diagnose.html')

    @app.route('/click-test')
    def click_test():
        """点击测试页面"""
        return send_from_directory('.', 'click_test.html')
    
    @app.route('/api/printers')
    def get_printers():
        """获取可用打印机列表"""
        try:
            # 更新打印机配置
            printer_service.update_printer_configs()
            
            # 获取数据库中的打印机配置
            printers = PrinterConfig.query.filter_by(is_available=True).all()
            printer_list = [printer.to_dict() for printer in printers]
            
            return jsonify({
                'success': True,
                'printers': printer_list
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/upload', methods=['POST'])
    def upload_file():
        """文件上传"""
        try:
            if 'file' not in request.files:
                return jsonify({'success': False, 'error': '没有选择文件'}), 400
            
            file = request.files['file']
            if file.filename == '':
                return jsonify({'success': False, 'error': '没有选择文件'}), 400
            
            # 保存文件
            file_info = file_processor.save_uploaded_file(file)
            
            # 获取文档信息
            doc_info = file_processor.get_document_info(
                file_info['file_path'], 
                file_info['file_type']
            )
            
            # 生成预览
            preview_result = preview_service.generate_preview(
                file_info['file_path'],
                file_info['file_type']
            )
            
            return jsonify({
                'success': True,
                'file_info': file_info,
                'document_info': doc_info,
                'preview': preview_result
            })
            
        except ValueError as e:
            return jsonify({'success': False, 'error': str(e)}), 400
        except RequestEntityTooLarge:
            return jsonify({'success': False, 'error': '文件太大，请选择小于50MB的文件'}), 413
        except Exception as e:
            return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500
    
    @app.route('/api/preview/<filename>')
    def get_preview_image(filename):
        """获取预览图片"""
        try:
            return send_from_directory(app.config['PREVIEW_FOLDER'], filename)
        except Exception as e:
            return jsonify({'error': str(e)}), 404
    
    @app.route('/api/print', methods=['POST'])
    def submit_print_job():
        """提交打印任务"""
        try:
            data = request.get_json()
            
            # 验证必需字段
            required_fields = ['filename', 'printer_name']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'缺少必需字段: {field}'}), 400
            
            # 验证文件是否存在
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], data['filename'])
            if not os.path.exists(file_path):
                return jsonify({'success': False, 'error': '文件不存在'}), 404
            
            # 获取文件信息
            file_size = os.path.getsize(file_path)
            file_type = file_processor.get_file_type(file_path)
            
            # 创建打印任务
            print_job = PrintJob(
                filename=data['filename'],
                original_filename=data.get('original_filename', data['filename']),
                file_path=file_path,
                file_size=file_size,
                file_type=file_type,
                printer_name=data['printer_name'],
                copies=data.get('copies', 1),
                page_range=data.get('page_range'),
                color_mode=data.get('color_mode', 'color'),
                duplex=data.get('duplex', 'none'),
                paper_size=data.get('paper_size', 'A4'),
                orientation=data.get('orientation', 'portrait'),
                status='pending'
            )
            
            db.session.add(print_job)
            db.session.commit()
            
            # 启动打印任务（在后台线程中）
            threading.Thread(
                target=process_print_job,
                args=(app, print_job.id, printer_service),
                daemon=True
            ).start()
            
            return jsonify({
                'success': True,
                'job_id': print_job.id,
                'message': '打印任务已提交'
            })
            
        except Exception as e:
            return jsonify({'success': False, 'error': f'提交打印任务失败: {str(e)}'}), 500
    
    @app.route('/api/jobs')
    def get_print_jobs():
        """获取打印任务列表"""
        try:
            # 获取最近的打印任务
            jobs = PrintJob.query.order_by(PrintJob.created_at.desc()).limit(50).all()
            job_list = [job.to_dict() for job in jobs]
            
            return jsonify({
                'success': True,
                'jobs': job_list
            })
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/jobs/<int:job_id>')
    def get_print_job(job_id):
        """获取特定打印任务信息"""
        try:
            job = PrintJob.query.get_or_404(job_id)
            return jsonify({
                'success': True,
                'job': job.to_dict()
            })
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/jobs/<int:job_id>/cancel', methods=['POST'])
    def cancel_print_job(job_id):
        """取消打印任务"""
        try:
            job = PrintJob.query.get_or_404(job_id)
            
            if job.status in ['pending', 'printing']:
                job.status = 'cancelled'
                job.completed_at = datetime.utcnow()
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': '打印任务已取消'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '无法取消已完成或失败的任务'
                }), 400
                
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.errorhandler(413)
    def too_large(e):
        return jsonify({'success': False, 'error': '文件太大，请选择小于50MB的文件'}), 413
    
    @app.errorhandler(404)
    def not_found(e):
        return jsonify({'success': False, 'error': '资源未找到'}), 404
    
    @app.errorhandler(500)
    def internal_error(e):
        return jsonify({'success': False, 'error': '服务器内部错误'}), 500
    
    return app


def process_print_job(app, job_id, printer_service):
    """处理打印任务（后台线程）"""
    with app.app_context():
        try:
            job = PrintJob.query.get(job_id)
            if not job or job.status != 'pending':
                return
            
            # 更新任务状态为打印中
            job.status = 'printing'
            job.started_at = datetime.utcnow()
            db.session.commit()
            
            # 准备打印选项
            print_options = {
                'copies': job.copies,
                'page_range': job.page_range,
                'color_mode': job.color_mode,
                'duplex': job.duplex,
                'paper_size': job.paper_size,
                'orientation': job.orientation
            }
            
            # 执行打印
            printer_service.print_file(job.file_path, job.printer_name, print_options)
            
            # 更新任务状态为完成
            job.status = 'completed'
            job.completed_at = datetime.utcnow()
            db.session.commit()
            
        except Exception as e:
            # 更新任务状态为失败
            job.status = 'failed'
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            db.session.commit()


if __name__ == '__main__':
    app = create_app()
    app.run(
        host=app.config['HOST'],
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )

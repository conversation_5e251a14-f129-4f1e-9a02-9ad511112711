import os
from datetime import timedelta

class Config:
    """应用配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///print_server.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {'pdf', 'docx', 'xlsx', 'doc', 'xls', 'txt', 'jpg', 'jpeg', 'png'}
    
    # 预览配置
    PREVIEW_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'previews')
    PREVIEW_DPI = 150  # 预览图片DPI
    PREVIEW_MAX_PAGES = 20  # 最大预览页数
    
    # 打印配置
    DEFAULT_PRINTER = None
    PRINT_TIMEOUT = 300  # 打印超时时间(秒)
    
    # 文件清理配置
    FILE_RETENTION_HOURS = 24  # 文件保留时间(小时)
    CLEANUP_INTERVAL_MINUTES = 60  # 清理检查间隔(分钟)
    
    # 应用配置
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', 8080))
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.PREVIEW_FOLDER, exist_ok=True)


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False


config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单文件上传测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 40px; 
            text-align: center; 
            margin: 20px 0;
            cursor: pointer;
            background: #f9f9f9;
        }
        .upload-area:hover { border-color: #007bff; background: #f0f8ff; }
        .btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            cursor: pointer; 
            border-radius: 4px;
            margin: 10px;
        }
        .btn:hover { background: #0056b3; }
        .log { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            max-height: 300px; 
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .progress { 
            width: 100%; 
            height: 20px; 
            background: #f0f0f0; 
            border-radius: 10px; 
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar { 
            height: 100%; 
            background: #007bff; 
            width: 0%; 
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <h1>文件上传测试页面</h1>
    
    <div class="upload-area" id="uploadArea">
        <h3>📁 点击或拖拽文件到此处</h3>
        <p>支持: PDF, Word, Excel, 图片, 文本文件</p>
        <input type="file" id="fileInput" style="display: none;" accept=".pdf,.docx,.xlsx,.doc,.xls,.txt,.jpg,.jpeg,.png">
        <button type="button" class="btn" id="selectBtn">选择文件</button>
    </div>
    
    <div id="progressContainer" style="display: none;">
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <p id="progressText">上传中...</p>
    </div>
    
    <div>
        <h3>操作日志:</h3>
        <div id="log" class="log"></div>
        <button type="button" class="btn" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            const progressBar = document.getElementById('progressBar');
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);
            return interval;
        }

        function hideProgress(interval) {
            if (interval) clearInterval(interval);
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = '100%';
            setTimeout(() => {
                document.getElementById('progressContainer').style.display = 'none';
                progressBar.style.width = '0%';
            }, 500);
        }

        function uploadFile(file) {
            log(`开始上传: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            
            const formData = new FormData();
            formData.append('file', file);
            
            const interval = showProgress();
            
            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                log(`服务器响应: ${response.status} ${response.statusText}`);
                return response.json();
            })
            .then(data => {
                hideProgress(interval);
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    log('✅ 上传成功!');
                    log(`文件信息: ${JSON.stringify(data.file_info, null, 2)}`);
                    if (data.preview && data.preview.success) {
                        log(`预览生成成功: ${data.preview.files.length} 个预览文件`);
                    }
                } else {
                    log(`❌ 上传失败: ${data.error}`);
                }
            })
            .catch(error => {
                hideProgress(interval);
                log(`❌ 请求失败: ${error.message}`);
                console.error('Upload error:', error);
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const selectBtn = document.getElementById('selectBtn');
            
            // 选择文件按钮
            selectBtn.addEventListener('click', function() {
                log('点击选择文件按钮');
                fileInput.click();
            });
            
            // 点击上传区域
            uploadArea.addEventListener('click', function(e) {
                if (e.target !== selectBtn) {
                    log('点击上传区域');
                    fileInput.click();
                }
            });
            
            // 文件选择
            fileInput.addEventListener('change', function(e) {
                log('文件选择事件触发');
                if (e.target.files && e.target.files.length > 0) {
                    const file = e.target.files[0];
                    log(`选择文件: ${file.name}`);
                    uploadFile(file);
                }
            });
            
            // 拖拽事件
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
                uploadArea.style.backgroundColor = '#e7f1ff';
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#ccc';
                uploadArea.style.backgroundColor = '#f9f9f9';
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                log('文件拖拽放下');
                uploadArea.style.borderColor = '#ccc';
                uploadArea.style.backgroundColor = '#f9f9f9';
                
                if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                    const file = e.dataTransfer.files[0];
                    log(`拖拽文件: ${file.name}`);
                    uploadFile(file);
                }
            });
            
            log('事件监听器设置完成');
        });
    </script>
</body>
</html>

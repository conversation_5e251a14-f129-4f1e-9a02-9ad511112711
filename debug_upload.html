<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover { border-color: #007bff; }
        .log { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            max-height: 300px; 
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            cursor: pointer; 
            border-radius: 4px;
        }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>文件上传调试页面</h1>
    
    <div class="upload-area" id="uploadArea">
        <p>点击这里或拖拽文件到此处上传</p>
        <input type="file" id="fileInput" style="display: none;">
        <button type="button" class="btn" onclick="document.getElementById('fileInput').click()">
            选择文件
        </button>
    </div>
    
    <div>
        <h3>调试日志:</h3>
        <div id="log" class="log"></div>
        <button type="button" class="btn" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            log(`元素检查: uploadArea=${!!uploadArea}, fileInput=${!!fileInput}`);
            
            // 点击上传区域
            uploadArea.addEventListener('click', function(e) {
                log('上传区域被点击');
                if (e.target.tagName !== 'BUTTON') {
                    fileInput.click();
                }
            });
            
            // 文件选择
            fileInput.addEventListener('change', function(e) {
                log(`文件选择事件触发，文件数量: ${e.target.files.length}`);
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    log(`选择的文件: ${file.name}, 大小: ${file.size}, 类型: ${file.type}`);
                    uploadFile(file);
                }
            });
            
            // 拖拽事件
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                log('拖拽悬停');
                uploadArea.style.borderColor = '#007bff';
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                log('拖拽离开');
                uploadArea.style.borderColor = '#ccc';
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                log('文件拖拽放下');
                uploadArea.style.borderColor = '#ccc';
                
                if (e.dataTransfer.files.length > 0) {
                    const file = e.dataTransfer.files[0];
                    log(`拖拽的文件: ${file.name}, 大小: ${file.size}, 类型: ${file.type}`);
                    uploadFile(file);
                }
            });
        });

        function uploadFile(file) {
            log(`开始上传文件: ${file.name}`);
            
            const formData = new FormData();
            formData.append('file', file);
            
            log('创建FormData完成，发送请求...');
            
            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                log(`收到响应，状态码: ${response.status}`);
                return response.json();
            })
            .then(data => {
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                if (data.success) {
                    log('✅ 上传成功!');
                } else {
                    log(`❌ 上传失败: ${data.error}`);
                }
            })
            .catch(error => {
                log(`❌ 请求失败: ${error.message}`);
                console.error('Upload error:', error);
            });
        }
    </script>
</body>
</html>

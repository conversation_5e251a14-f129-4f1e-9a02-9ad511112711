<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}局域网打印服务{% endblock %}</title>
    
    <!-- 内联基础样式 -->
    <style>
        /* 基础Bootstrap样式 */
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
        .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; padding: 0 15px; }
        .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; padding: 0 15px; }
        .col-md-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
        @media (max-width: 768px) {
            .col-lg-8, .col-lg-4, .col-md-6 { flex: 0 0 100%; max-width: 100%; }
        }

        /* 基础组件样式 */
        .btn { display: inline-block; padding: 0.375rem 0.75rem; margin-bottom: 0; font-size: 1rem; font-weight: 400; line-height: 1.5; text-align: center; text-decoration: none; vertical-align: middle; cursor: pointer; border: 1px solid transparent; border-radius: 0.25rem; transition: all 0.15s ease-in-out; }
        .btn-primary { color: #fff; background-color: #0d6efd; border-color: #0d6efd; }
        .btn-primary:hover { background-color: #0b5ed7; border-color: #0a58ca; }
        .btn-success { color: #fff; background-color: #198754; border-color: #198754; }
        .btn-outline-primary { color: #0d6efd; border-color: #0d6efd; background-color: transparent; }
        .btn-outline-primary:hover { color: #fff; background-color: #0d6efd; }
        .btn-lg { padding: 0.5rem 1rem; font-size: 1.25rem; border-radius: 0.3rem; }
        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; border-radius: 0.2rem; }

        .card { position: relative; display: flex; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #fff; background-clip: border-box; border: 1px solid rgba(0,0,0,.125); border-radius: 0.25rem; margin-bottom: 1rem; }
        .card-header { padding: 0.5rem 1rem; margin-bottom: 0; background-color: rgba(0,0,0,.03); border-bottom: 1px solid rgba(0,0,0,.125); }
        .card-body { flex: 1 1 auto; padding: 1rem; }
        .card-title { margin-bottom: 0.5rem; }

        .form-control, .form-select { display: block; width: 100%; padding: 0.375rem 0.75rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: #212529; background-color: #fff; background-image: none; border: 1px solid #ced4da; border-radius: 0.25rem; transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out; }
        .form-label { margin-bottom: 0.5rem; font-weight: 600; }

        .d-none { display: none !important; }
        .d-block { display: block !important; }
        .d-flex { display: flex !important; }
        .justify-content-between { justify-content: space-between !important; }
        .align-items-center { align-items: center !important; }
        .text-center { text-align: center !important; }
        .text-muted { color: #6c757d !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .mb-3 { margin-bottom: 1rem !important; }
        .mb-4 { margin-bottom: 1.5rem !important; }
        .me-2 { margin-right: 0.5rem !important; }
        .mt-2 { margin-top: 0.5rem !important; }
        .my-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !important; }

        .progress { display: flex; height: 1rem; overflow: hidden; font-size: 0.75rem; background-color: #e9ecef; border-radius: 0.25rem; }
        .progress-bar { display: flex; flex-direction: column; justify-content: center; overflow: hidden; color: #fff; text-align: center; white-space: nowrap; background-color: #0d6efd; transition: width 0.6s ease; }
        .progress-bar-striped { background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent); background-size: 1rem 1rem; }
        .progress-bar-animated { animation: progress-bar-stripes 1s linear infinite; }
        @keyframes progress-bar-stripes { 0% { background-position-x: 1rem; } }

        /* 图标字体替代 */
        .fas::before { font-family: monospace; }
        .fa-upload::before { content: "📁"; }
        .fa-print::before { content: "🖨️"; }
        .fa-eye::before { content: "👁️"; }
        .fa-cog::before { content: "⚙️"; }
        .fa-list::before { content: "📋"; }
        .fa-history::before { content: "🕒"; }
        .fa-sync-alt::before { content: "🔄"; }
        .fa-search-minus::before { content: "🔍-"; }
        .fa-search-plus::before { content: "🔍+"; }
        .fa-expand-arrows-alt::before { content: "⤢"; }
        .fa-folder-open::before { content: "📂"; }
        .fa-cloud-upload-alt::before { content: "☁️⬆️"; }
        .fa-spinner::before { content: "⏳"; }
        .fa-times::before { content: "❌"; }
        .fa-check-circle::before { content: "✅"; }
        .fa-exclamation-circle::before { content: "⚠️"; }
        .fa-info-circle::before { content: "ℹ️"; }
        .fa-home::before { content: "🏠"; }
    </style>
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: bold;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f1ff;
        }
        
        .preview-container {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .preview-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }
        
        .job-status {
            font-weight: bold;
        }
        
        .job-status.pending {
            color: #ffc107;
        }
        
        .job-status.printing {
            color: #0d6efd;
        }
        
        .job-status.completed {
            color: #198754;
        }
        
        .job-status.failed {
            color: #dc3545;
        }
        
        .job-status.cancelled {
            color: #6c757d;
        }
        
        .progress-container {
            margin-top: 1rem;
        }
        
        .file-info {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .print-config {
            background-color: #ffffff;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .btn-group-toggle .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .footer {
            margin-top: 3rem;
            padding: 2rem 0;
            border-top: 1px solid #dee2e6;
            background-color: #ffffff;
        }
        
        @media (max-width: 768px) {
            .upload-area {
                padding: 2rem 1rem;
            }
            
            .print-config {
                padding: 1rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-print me-2"></i>局域网打印服务
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPrintJobs()">
                            <i class="fas fa-list me-1"></i>打印任务
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>局域网打印服务</h6>
                    <p class="text-muted small">
                        支持PDF、Word、Excel等多种文件格式的在线打印服务
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        仅限局域网访问 | 文件自动清理
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Toast 通知容器 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>
    
    <!-- 简化的Bootstrap JS功能 -->
    <script>
        // 简化的Modal实现
        class SimpleModal {
            constructor(element) {
                this.element = element;
            }
            show() {
                this.element.style.display = 'block';
                this.element.style.position = 'fixed';
                this.element.style.top = '50%';
                this.element.style.left = '50%';
                this.element.style.transform = 'translate(-50%, -50%)';
                this.element.style.zIndex = '1050';
                this.element.style.backgroundColor = 'white';
                this.element.style.border = '1px solid #ccc';
                this.element.style.borderRadius = '0.25rem';
                this.element.style.boxShadow = '0 0.5rem 1rem rgba(0,0,0,0.15)';
                this.element.style.maxWidth = '90vw';
                this.element.style.maxHeight = '90vh';
                this.element.style.overflow = 'auto';

                // 创建背景遮罩
                const backdrop = document.createElement('div');
                backdrop.style.position = 'fixed';
                backdrop.style.top = '0';
                backdrop.style.left = '0';
                backdrop.style.width = '100%';
                backdrop.style.height = '100%';
                backdrop.style.backgroundColor = 'rgba(0,0,0,0.5)';
                backdrop.style.zIndex = '1040';
                backdrop.className = 'modal-backdrop';
                document.body.appendChild(backdrop);

                // 点击背景关闭
                backdrop.onclick = () => this.hide();

                // ESC键关闭
                this.escHandler = (e) => {
                    if (e.key === 'Escape') this.hide();
                };
                document.addEventListener('keydown', this.escHandler);
            }
            hide() {
                this.element.style.display = 'none';
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) backdrop.remove();
                if (this.escHandler) {
                    document.removeEventListener('keydown', this.escHandler);
                }
            }
        }

        // 简化的Toast实现
        class SimpleToast {
            constructor(element) {
                this.element = element;
            }
            show() {
                this.element.style.display = 'block';
                this.element.style.position = 'fixed';
                this.element.style.bottom = '20px';
                this.element.style.right = '20px';
                this.element.style.zIndex = '1060';
                this.element.style.minWidth = '300px';
                this.element.style.backgroundColor = 'white';
                this.element.style.border = '1px solid #ccc';
                this.element.style.borderRadius = '0.25rem';
                this.element.style.boxShadow = '0 0.5rem 1rem rgba(0,0,0,0.15)';

                // 自动隐藏
                setTimeout(() => {
                    this.element.style.display = 'none';
                }, 5000);
            }
        }

        // 全局bootstrap对象模拟
        window.bootstrap = {
            Modal: SimpleModal,
            Toast: SimpleToast
        };
    </script>
    
    <!-- 通用JavaScript -->
    <script>
        // 显示Toast通知
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            const toastBody = toast.querySelector('.toast-body');
            const toastIcon = toast.querySelector('.fas');
            
            toastBody.textContent = message;
            
            // 设置图标和颜色
            toastIcon.className = 'fas me-2';
            if (type === 'success') {
                toastIcon.classList.add('fa-check-circle', 'text-success');
            } else if (type === 'error') {
                toastIcon.classList.add('fa-exclamation-circle', 'text-danger');
            } else if (type === 'warning') {
                toastIcon.classList.add('fa-exclamation-triangle', 'text-warning');
            } else {
                toastIcon.classList.add('fa-info-circle', 'text-primary');
            }
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
        
        // AJAX请求封装
        function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                },
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            return fetch(url, finalOptions)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('API request failed:', error);
                    throw error;
                });
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

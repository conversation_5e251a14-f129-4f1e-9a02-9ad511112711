{% extends "base.html" %}

{% block title %}首页 - 局域网打印服务{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：文件上传和预览 -->
    <div class="col-lg-8">
        <!-- 文件上传区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>文件上传
                </h5>
            </div>
            <div class="card-body">
                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>拖拽文件到此处或点击选择文件</h5>
                    <p class="text-muted">
                        支持格式: PDF, Word(.docx), Excel(.xlsx), 图片(JPG, PNG)<br>
                        最大文件大小: 50MB
                    </p>
                    <input type="file" id="fileInput" class="d-none" accept=".pdf,.docx,.xlsx,.doc,.xls,.txt,.jpg,.jpeg,.png" onchange="handleFileChange(this)">
                    <button type="button" id="selectFileBtn" class="btn btn-primary" onclick="event.stopPropagation(); document.getElementById('fileInput').click()">
                        <i class="fas fa-folder-open me-2"></i>选择文件
                    </button>
                </div>
                
                <!-- 上传进度 -->
                <div id="uploadProgress" class="progress-container d-none">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted mt-2 d-block">正在上传文件...</small>
                </div>
            </div>
        </div>
        
        <!-- 文件信息 -->
        <div id="fileInfoCard" class="card mb-4 d-none">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>文件信息
                </h5>
            </div>
            <div class="card-body">
                <div id="fileInfo" class="file-info"></div>
            </div>
        </div>
        
        <!-- 文件预览 -->
        <div id="previewCard" class="card mb-4 d-none">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>文件预览
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetZoom()">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="previewContainer" class="preview-container"></div>
                <div id="previewPagination" class="d-flex justify-content-center mt-3"></div>
            </div>
        </div>
    </div>
    
    <!-- 右侧：打印配置 -->
    <div class="col-lg-4">
        <!-- 打印机选择 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-print me-2"></i>打印机
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshPrinters()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="card-body">
                <select id="printerSelect" class="form-select">
                    <option value="">正在加载打印机...</option>
                </select>
                <small class="text-muted">选择要使用的打印机</small>
            </div>
        </div>
        
        <!-- 打印配置 -->
        <div id="printConfigCard" class="card mb-4 d-none">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>打印设置
                </h5>
            </div>
            <div class="card-body print-config">
                <!-- 打印份数 -->
                <div class="mb-3">
                    <label for="copies" class="form-label">打印份数</label>
                    <input type="number" id="copies" class="form-control" value="1" min="1" max="99">
                </div>
                
                <!-- 页面范围 -->
                <div class="mb-3">
                    <label class="form-label">页面范围</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="pageRange" id="allPages" value="all" checked>
                        <label class="btn btn-outline-primary" for="allPages">全部页面</label>
                        
                        <input type="radio" class="btn-check" name="pageRange" id="customPages" value="custom">
                        <label class="btn btn-outline-primary" for="customPages">自定义</label>
                    </div>
                    <input type="text" id="customPageRange" class="form-control mt-2 d-none" 
                           placeholder="例如: 1-5,8,10-12">
                    <small class="text-muted">自定义格式: 1-5,8,10-12</small>
                </div>
                
                <!-- 颜色模式 -->
                <div class="mb-3">
                    <label class="form-label">颜色模式</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="colorMode" id="colorPrint" value="color" checked>
                        <label class="btn btn-outline-primary" for="colorPrint">彩色</label>
                        
                        <input type="radio" class="btn-check" name="colorMode" id="grayscalePrint" value="grayscale">
                        <label class="btn btn-outline-primary" for="grayscalePrint">黑白</label>
                    </div>
                </div>
                
                <!-- 双面打印 -->
                <div class="mb-3">
                    <label class="form-label">双面打印</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="duplex" id="noDuplex" value="none" checked>
                        <label class="btn btn-outline-primary" for="noDuplex">单面</label>
                        
                        <input type="radio" class="btn-check" name="duplex" id="longEdge" value="long-edge">
                        <label class="btn btn-outline-primary" for="longEdge">长边翻转</label>
                        
                        <input type="radio" class="btn-check" name="duplex" id="shortEdge" value="short-edge">
                        <label class="btn btn-outline-primary" for="shortEdge">短边翻转</label>
                    </div>
                </div>
                
                <!-- 纸张大小 -->
                <div class="mb-3">
                    <label for="paperSize" class="form-label">纸张大小</label>
                    <select id="paperSize" class="form-select">
                        <option value="A4" selected>A4</option>
                        <option value="A3">A3</option>
                        <option value="Letter">Letter</option>
                        <option value="Legal">Legal</option>
                    </select>
                </div>
                
                <!-- 打印方向 -->
                <div class="mb-3">
                    <label class="form-label">打印方向</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="orientation" id="portrait" value="portrait" checked>
                        <label class="btn btn-outline-primary" for="portrait">纵向</label>
                        
                        <input type="radio" class="btn-check" name="orientation" id="landscape" value="landscape">
                        <label class="btn btn-outline-primary" for="landscape">横向</label>
                    </div>
                </div>
                
                <!-- 打印按钮 -->
                <div class="d-grid">
                    <button type="button" id="printButton" class="btn btn-success btn-lg" onclick="submitPrintJob()" disabled>
                        <i class="fas fa-print me-2"></i>开始打印
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 最近打印任务 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>最近任务
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="showPrintJobs()">
                    <i class="fas fa-list"></i> 查看全部
                </button>
            </div>
            <div class="card-body">
                <div id="recentJobs">
                    <p class="text-muted text-center">暂无打印任务</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 打印任务模态框 -->
<div class="modal fade" id="jobsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list me-2"></i>打印任务列表
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="jobsList">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 立即定义文件处理函数，确保onclick事件可用
function handleFileChange(input) {
    console.log('File change handler called', input.files);
    if (input.files && input.files.length > 0) {
        const file = input.files[0];
        console.log('File selected:', file.name, file.size, file.type);

        // 如果主JavaScript已加载，使用主函数
        if (typeof window.handleFileSelect === 'function') {
            window.handleFileSelect([file]);
        } else if (typeof uploadFile === 'function' && typeof validateFile === 'function') {
            if (validateFile(file)) {
                uploadFile(file);
            }
        } else {
            // 备用处理：直接上传
            console.log('Using fallback upload method');
            uploadFileFallback(file);
        }
    }
}

// 备用上传函数
function uploadFileFallback(file) {
    console.log('Fallback upload for:', file.name);

    const formData = new FormData();
    formData.append('file', file);

    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Upload response:', data);
        if (data.success) {
            alert('文件上传成功！');
            // 刷新页面以显示结果
            window.location.reload();
        } else {
            alert('上传失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        alert('上传失败: ' + error.message);
    });
}
</script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
{% endblock %}

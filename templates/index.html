{% extends "base.html" %}

{% block title %}首页 - 局域网打印服务{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：文件上传和预览 -->
    <div class="col-lg-8">
        <!-- 文件上传区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>文件上传
                </h5>
            </div>
            <div class="card-body">
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>拖拽文件到此处或点击选择文件</h5>
                    <p class="text-muted">
                        支持格式: PDF, Word(.docx), Excel(.xlsx), 图片(JPG, PNG)<br>
                        最大文件大小: 50MB
                    </p>
                    <input type="file" id="fileInput" style="display: none;" accept=".pdf,.docx,.xlsx,.doc,.xls,.txt,.jpg,.jpeg,.png">
                    <button type="button" id="selectFileBtn" class="btn btn-primary">
                        <i class="fas fa-folder-open me-2"></i>选择文件
                    </button>
                </div>
                
                <!-- 上传进度 -->
                <div id="uploadProgress" class="progress-container d-none">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted mt-2 d-block">正在上传文件...</small>
                </div>
            </div>
        </div>
        
        <!-- 文件信息 -->
        <div id="fileInfoCard" class="card mb-4 d-none">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>文件信息
                </h5>
            </div>
            <div class="card-body">
                <div id="fileInfo" class="file-info"></div>
            </div>
        </div>
        
        <!-- 文件预览 -->
        <div id="previewCard" class="card mb-4 d-none">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>文件预览
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetZoom()">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="previewContainer" class="preview-container"></div>
                <div id="previewPagination" class="d-flex justify-content-center mt-3"></div>
            </div>
        </div>
    </div>
    
    <!-- 右侧：打印配置 -->
    <div class="col-lg-4">
        <!-- 打印机选择 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-print me-2"></i>打印机
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshPrinters()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="card-body">
                <select id="printerSelect" class="form-select">
                    <option value="">正在加载打印机...</option>
                </select>
                <small class="text-muted">选择要使用的打印机</small>
            </div>
        </div>
        
        <!-- 打印配置 -->
        <div id="printConfigCard" class="card mb-4 d-none">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>打印设置
                </h5>
            </div>
            <div class="card-body print-config">
                <!-- 打印份数 -->
                <div class="mb-3">
                    <label for="copies" class="form-label">打印份数</label>
                    <input type="number" id="copies" class="form-control" value="1" min="1" max="99">
                </div>
                
                <!-- 页面范围 -->
                <div class="mb-3">
                    <label class="form-label">页面范围</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="pageRange" id="allPages" value="all" checked>
                        <label class="btn btn-outline-primary" for="allPages">全部页面</label>
                        
                        <input type="radio" class="btn-check" name="pageRange" id="customPages" value="custom">
                        <label class="btn btn-outline-primary" for="customPages">自定义</label>
                    </div>
                    <input type="text" id="customPageRange" class="form-control mt-2 d-none" 
                           placeholder="例如: 1-5,8,10-12">
                    <small class="text-muted">自定义格式: 1-5,8,10-12</small>
                </div>
                
                <!-- 颜色模式 -->
                <div class="mb-3">
                    <label class="form-label">颜色模式</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="colorMode" id="colorPrint" value="color" checked>
                        <label class="btn btn-outline-primary" for="colorPrint">彩色</label>
                        
                        <input type="radio" class="btn-check" name="colorMode" id="grayscalePrint" value="grayscale">
                        <label class="btn btn-outline-primary" for="grayscalePrint">黑白</label>
                    </div>
                </div>
                
                <!-- 双面打印 -->
                <div class="mb-3">
                    <label class="form-label">双面打印</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="duplex" id="noDuplex" value="none" checked>
                        <label class="btn btn-outline-primary" for="noDuplex">单面</label>
                        
                        <input type="radio" class="btn-check" name="duplex" id="longEdge" value="long-edge">
                        <label class="btn btn-outline-primary" for="longEdge">长边翻转</label>
                        
                        <input type="radio" class="btn-check" name="duplex" id="shortEdge" value="short-edge">
                        <label class="btn btn-outline-primary" for="shortEdge">短边翻转</label>
                    </div>
                </div>
                
                <!-- 纸张大小 -->
                <div class="mb-3">
                    <label for="paperSize" class="form-label">纸张大小</label>
                    <select id="paperSize" class="form-select">
                        <option value="A4" selected>A4</option>
                        <option value="A3">A3</option>
                        <option value="Letter">Letter</option>
                        <option value="Legal">Legal</option>
                    </select>
                </div>
                
                <!-- 打印方向 -->
                <div class="mb-3">
                    <label class="form-label">打印方向</label>
                    <div class="btn-group-toggle">
                        <input type="radio" class="btn-check" name="orientation" id="portrait" value="portrait" checked>
                        <label class="btn btn-outline-primary" for="portrait">纵向</label>
                        
                        <input type="radio" class="btn-check" name="orientation" id="landscape" value="landscape">
                        <label class="btn btn-outline-primary" for="landscape">横向</label>
                    </div>
                </div>
                
                <!-- 打印按钮 -->
                <div class="d-grid">
                    <button type="button" id="printButton" class="btn btn-success btn-lg" onclick="submitPrintJob()" disabled>
                        <i class="fas fa-print me-2"></i>开始打印
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 最近打印任务 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>最近任务
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="showPrintJobs()">
                    <i class="fas fa-list"></i> 查看全部
                </button>
            </div>
            <div class="card-body">
                <div id="recentJobs">
                    <p class="text-muted text-center">暂无打印任务</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 打印任务模态框 -->
<div class="modal fade" id="jobsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list me-2"></i>打印任务列表
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="jobsList">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全新的文件上传实现 - 简单可靠
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化文件上传功能...');

    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const selectBtn = document.getElementById('selectFileBtn');

    if (!uploadArea || !fileInput || !selectBtn) {
        console.error('找不到必要的元素');
        return;
    }

    console.log('元素找到，设置事件监听器...');

    // 1. 选择文件按钮点击事件
    selectBtn.addEventListener('click', function(e) {
        console.log('选择文件按钮被点击');
        e.preventDefault();
        e.stopPropagation();
        fileInput.click();
    });

    // 2. 上传区域点击事件
    uploadArea.addEventListener('click', function(e) {
        console.log('上传区域被点击');
        // 如果点击的不是按钮，就触发文件选择
        if (e.target !== selectBtn && !selectBtn.contains(e.target)) {
            fileInput.click();
        }
    });

    // 3. 文件选择事件
    fileInput.addEventListener('change', function(e) {
        console.log('文件选择事件触发');
        if (e.target.files && e.target.files.length > 0) {
            const file = e.target.files[0];
            console.log('选择的文件:', file.name, file.size, file.type);
            handleFileUpload(file);
        }
    });

    // 4. 拖拽事件
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.style.backgroundColor = '#e7f1ff';
        uploadArea.style.borderColor = '#007bff';
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.style.backgroundColor = '';
        uploadArea.style.borderColor = '';
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.style.backgroundColor = '';
        uploadArea.style.borderColor = '';

        console.log('文件拖拽放下');
        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            const file = e.dataTransfer.files[0];
            console.log('拖拽的文件:', file.name);
            handleFileUpload(file);
        }
    });

    console.log('文件上传功能初始化完成');
});

// 文件上传处理函数
function handleFileUpload(file) {
    console.log('开始处理文件上传:', file.name);

    // 文件验证
    if (!validateFile(file)) {
        return;
    }

    // 显示上传进度
    showUploadProgress();

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);

    // 上传文件
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('服务器响应:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('上传响应:', data);
        hideUploadProgress();

        if (data.success) {
            showToast('文件上传成功！', 'success');
            displayFileInfo(data.file_info, data.document_info);
            displayPreview(data.preview);
            enablePrintOptions();
        } else {
            showToast('上传失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('上传错误:', error);
        hideUploadProgress();
        showToast('上传失败: ' + error.message, 'error');
    });
}

// 文件验证
function validateFile(file) {
    // 检查文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
        showToast('文件太大，请选择小于50MB的文件', 'error');
        return false;
    }

    // 检查文件类型
    const allowedExtensions = ['.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt', '.jpg', '.jpeg', '.png'];
    const fileName = file.name.toLowerCase();
    const isValidType = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidType) {
        showToast('不支持的文件格式', 'error');
        return false;
    }

    return true;
}

// 显示上传进度
function showUploadProgress() {
    const progressDiv = document.getElementById('uploadProgress');
    if (progressDiv) {
        progressDiv.classList.remove('d-none');
    }
}

// 隐藏上传进度
function hideUploadProgress() {
    const progressDiv = document.getElementById('uploadProgress');
    if (progressDiv) {
        progressDiv.classList.add('d-none');
    }
}

// 显示通知
function showToast(message, type = 'info') {
    // 简单的alert实现，后续可以改为更好的通知
    if (type === 'error') {
        alert('错误: ' + message);
    } else if (type === 'success') {
        alert('成功: ' + message);
    } else {
        alert(message);
    }
}

// 显示文件信息
function displayFileInfo(fileInfo, docInfo) {
    const fileInfoCard = document.getElementById('fileInfoCard');
    const fileInfoDiv = document.getElementById('fileInfo');

    if (fileInfoCard && fileInfoDiv) {
        const html = `
            <div class="row">
                <div class="col-md-6">
                    <strong>文件名:</strong> ${fileInfo.original_filename}<br>
                    <strong>文件大小:</strong> ${formatFileSize(fileInfo.file_size)}<br>
                    <strong>文件类型:</strong> ${fileInfo.file_type}
                </div>
                <div class="col-md-6">
                    <strong>页数:</strong> ${docInfo.pages || '未知'}<br>
                    <strong>上传时间:</strong> ${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
        fileInfoDiv.innerHTML = html;
        fileInfoCard.classList.remove('d-none');
    }
}

// 显示预览
function displayPreview(previewData) {
    const previewCard = document.getElementById('previewCard');
    const previewContainer = document.getElementById('previewContainer');

    if (previewCard && previewContainer) {
        if (previewData && previewData.success && previewData.files) {
            let html = '';
            previewData.files.forEach(file => {
                html += `
                    <div class="text-center mb-3">
                        <img src="/api/preview/${file.filename}"
                             class="img-fluid"
                             style="max-width: 100%; border: 1px solid #ddd; border-radius: 4px;"
                             alt="第${file.page}页预览">
                    </div>
                `;
            });
            previewContainer.innerHTML = html;
        } else {
            previewContainer.innerHTML = '<p class="text-center text-muted">无法生成预览</p>';
        }
        previewCard.classList.remove('d-none');
    }
}

// 启用打印选项
function enablePrintOptions() {
    const printConfigCard = document.getElementById('printConfigCard');
    if (printConfigCard) {
        printConfigCard.classList.remove('d-none');
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
{% endblock %}

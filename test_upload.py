#!/usr/bin/env python3
"""
测试文件上传功能
"""

import requests
import os

def test_upload():
    """测试文件上传API"""
    
    # 创建一个测试文件
    test_content = "这是一个测试文件\n用于测试上传功能\n"
    test_file_path = "test.txt"
    
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    try:
        # 测试上传
        url = "http://localhost:8080/api/upload"
        
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test.txt', f, 'text/plain')}
            response = requests.post(url, files=files)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 上传成功!")
                print(f"文件信息: {data.get('file_info')}")
            else:
                print(f"❌ 上传失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务器正在运行在 http://localhost:8080")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

if __name__ == '__main__':
    test_upload()

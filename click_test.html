<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点击测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-area { 
            border: 2px solid #007bff; 
            padding: 40px; 
            text-align: center; 
            margin: 20px 0;
            cursor: pointer;
            background: #f0f8ff;
        }
        .btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 15px 30px; 
            cursor: pointer; 
            border-radius: 5px;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover { background: #0056b3; }
        .log { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            max-height: 200px; 
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🖱️ 点击功能测试</h1>
    
    <div class="test-area" onclick="testClick('area')">
        <h3>点击这个区域</h3>
        <p>应该触发文件选择对话框</p>
        <input type="file" id="fileInput" style="display: none;" onchange="handleFile(this)">
        <button type="button" class="btn" onclick="event.stopPropagation(); testClick('button')">
            点击这个按钮
        </button>
    </div>
    
    <div>
        <h3>测试结果:</h3>
        <div id="log" class="log">等待点击测试...\n</div>
        <button type="button" class="btn" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function testClick(source) {
            log(`${source} 被点击了`);
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                log('触发文件选择对话框');
                fileInput.click();
            } else {
                log('错误: 找不到文件输入元素');
            }
        }

        function handleFile(input) {
            log('文件选择事件触发');
            if (input.files && input.files.length > 0) {
                const file = input.files[0];
                log(`选择的文件: ${file.name} (${(file.size/1024/1024).toFixed(2)} MB)`);
                
                // 测试上传
                uploadTest(file);
            } else {
                log('没有选择文件');
            }
        }

        function uploadTest(file) {
            log('开始测试上传...');
            
            const formData = new FormData();
            formData.append('file', file);
            
            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                log(`服务器响应: ${response.status}`);
                return response.json();
            })
            .then(data => {
                log(`上传结果: ${data.success ? '成功' : '失败'}`);
                if (data.error) {
                    log(`错误: ${data.error}`);
                }
            })
            .catch(error => {
                log(`上传失败: ${error.message}`);
            });
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，点击测试准备就绪');
        });
    </script>
</body>
</html>
